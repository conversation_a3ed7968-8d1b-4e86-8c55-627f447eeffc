import sqlite3
import datetime

DATABASE_URL = '/Users/<USER>/Source/PazarYeriServis/pazaryeri.db'

def get_db_connection():
    """Veritabanı bağlantısı oluşturur."""
    conn = sqlite3.connect(DATABASE_URL)
    conn.row_factory = sqlite3.Row
    return conn

def get_products_for_supplier(supplier_name):
    """Belirli bir tedarikçiye ait tüm ürünleri ve URL'leri çeker."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("""
        SELECT
            ps.id as product_supplier_id,
            ps.product_id,
            ps.supplier_product_url
        FROM product_suppliers ps
        JOIN suppliers s ON ps.supplier_id = s.id
        WHERE s.name = ? AND ps.is_deleted = 0
    """, (supplier_name,))
    products = cursor.fetchall()
    conn.close()
    return products

def update_price(product_supplier_id, new_price):
    """product_suppliers tablosundaki fiyatı ve son kontrol zamanını günceller."""
    if new_price is None:
        return

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("""
        UPDATE product_suppliers
        SET supplier_price = ?, last_checked = ?
        WHERE id = ?
    """, (new_price, datetime.datetime.now(), product_supplier_id))
    conn.commit()
    print(f"ID {product_supplier_id}: Fiyat güncellendi -> {new_price}")
    conn.close()

def add_or_update_product_and_supplier_link(product_name, brand_name, supplier_name, supplier_product_url):
    """Ürünü products tablosuna ekler/günceller ve product_suppliers tablosuna linki ekler/günceller."""
    conn = get_db_connection()
    cursor = conn.cursor()

    # 1. Tedarikçi ID'sini bul veya ekle
    cursor.execute("SELECT id FROM suppliers WHERE name = ?", (supplier_name,))
    supplier = cursor.fetchone()
    if supplier:
        supplier_id = supplier['id']
    else:
        cursor.execute("INSERT INTO suppliers (name) VALUES (?) RETURNING id", (supplier_name,))
        supplier_id = cursor.fetchone()[0]
        conn.commit()
        print(f"Yeni tedarikçi eklendi: {supplier_name} (ID: {supplier_id})")

    # 2. Ürün ID'sini bul veya ekle
    cursor.execute("SELECT id FROM products WHERE product_name = ? AND brand = ?", (product_name, brand_name))
    product = cursor.fetchone()
    if product:
        product_id = product['id']
    else:
        # Varsayılan değerlerle yeni ürün ekle
        cursor.execute("""
            INSERT INTO products (
                product_name, brand, sku, model, barcode, desi, stock_multiplier, profit_margin, commission_rate, 
                user_id, warehouse_id, gtip, description, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING id
        """, (
            product_name, brand_name, None, None, None, 0.0, 1, 0.0, 0.0, 
            None, None, None, None, datetime.datetime.now(), datetime.datetime.now()
        ))
        product_id = cursor.fetchone()[0]
        conn.commit()
        print(f"Yeni ürün eklendi: {product_name} (ID: {product_id})")

    # 3. product_suppliers tablosunu güncelle veya ekle
    cursor.execute("""
        SELECT id FROM product_suppliers 
        WHERE product_id = ? AND supplier_id = ?
    """, (product_id, supplier_id))
    product_supplier = cursor.fetchone()

    if product_supplier:
        product_supplier_id = product_supplier['id']
        cursor.execute("""
            UPDATE product_suppliers
            SET supplier_product_url = ?, last_checked = ?
            WHERE id = ?
        """, (supplier_product_url, datetime.datetime.now(), product_supplier_id))
        print(f"Mevcut product_supplier güncellendi: ID {product_supplier_id}")
    else:
        cursor.execute("""
            INSERT INTO product_suppliers (
                product_id, supplier_id, supplier_product_url, last_checked
            ) VALUES (?, ?, ?, ?)
            RETURNING id
        """, (
            product_id, supplier_id, supplier_product_url, datetime.datetime.now()
        ))
        product_supplier_id = cursor.fetchone()[0]
        print(f"Yeni product_supplier eklendi: ID {product_supplier_id}")
    
    conn.commit()
    conn.close()
    return product_id, supplier_id, product_supplier_id