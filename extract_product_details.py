import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import database_operations as db

async def extract_trendyol_product_details(url):
    """Trendyol ürün sayfasından ürün adı ve markasını çeker."""
    print(f"Ürün detayları çekiliyor: {url}")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        try:
            await page.goto(url, timeout=60000)

            # <PERSON><PERSON><PERSON>ri kabul et
            try:
                await page.locator('button#onetrust-accept-btn-handler').click(timeout=5000)
            except PlaywrightTimeoutError:
                pass # Pop-up yoksa devam et

            product_name = None
            brand_name = None

            # Ürün Adı
            try:
                product_name_locator = page.get_by_role("heading", level=1).first
                await product_name_locator.wait_for(state='visible', timeout=10000)
                product_name = await product_name_locator.inner_text()
            except PlaywrightTimeoutError:
                print("Ürün adı bulunamadı.")

            # Marka Adı
            try:
                brand_name_locator = page.locator("a.product-title-brand-name-anchor")
                await brand_name_locator.wait_for(state='visible', timeout=10000)
                brand_name = await brand_name_locator.inner_text()
            except PlaywrightTimeoutError:
                print("Marka adı bulunamadı.")

            await browser.close()
            return {"product_name": product_name, "brand_name": brand_name}

        except Exception as e:
            print(f"HATA: Ürün detayları çekilirken genel bir sorun oluştu - {url}. Hata: {e}")
            await browser.close()
            return None

async def main():
    trendyol_url = "https://www.trendyol.com/stanley/unisex-termos-the-iceflow-flip-straw-water-bottle-65l-22oz-p-97211714?boutiqueId=61&merchantId=1022324"
    details = await extract_trendyol_product_details(trendyol_url)
    if details and details['product_name'] and details['brand_name']:
        print("Çekilen Ürün Detayları:")
        print(f"  Ürün Adı: {details['product_name']}")
        print(f"  Marka: {details['brand_name']}")
        
        # Veritabanına ekle/güncelle
        product_id, supplier_id, product_supplier_id = db.add_or_update_product_and_supplier_link(
            details['product_name'], 
            details['brand_name'], 
            "Trendyol", # Tedarikçi adı
            trendyol_url
        )
        print(f"Veritabanı işlemi tamamlandı. Ürün ID: {product_id}, Tedarikçi ID: {supplier_id}, Product Supplier ID: {product_supplier_id}")
    else:
        print("Ürün detayları çekilemedi veya eksik.")

if __name__ == '__main__':
    asyncio.run(main())