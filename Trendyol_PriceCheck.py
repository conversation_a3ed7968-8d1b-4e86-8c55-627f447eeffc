
import asyncio
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
import database_operations as db

async def scrape_trendyol_price(url):
    """Verilen Trendyol URL'sinden Playwright kull<PERSON>rak fiyatı çeker."""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        try:
            await page.goto(url, timeout=60000)

            # <PERSON><PERSON><PERSON><PERSON> kabul et
            try:
                await page.locator('button#onetrust-accept-btn-handler').click(timeout=5000)
            except PlaywrightTimeoutError:
                pass # Pop-up yoksa devam et

            

            # Fiyatı oku
            price_locator = page.locator("//div[contains(@class, 'product-price-container')]//span[contains(@class, 'prc-slg-prc')]")
            await price_locator.first.wait_for(state='visible', timeout=15000)
            price_text = await price_locator.first.inner_text()

            if price_text:
                cleaned_price = price_text.replace('TL', '').replace('.', '').replace(',', '.').strip()
                final_price = float(cleaned_price)
                await browser.close()
                return final_price
            
            await browser.close()
            return None

        except Exception as e:
            print(f"HATA: Fiyat çekilirken genel bir sorun oluştu - {url}. Hata: {e}")
            await browser.close()
            return None

async def main():
    """Trendyol için ana işlem fonksiyonu."""
    print("Trendyol fiyat güncelleme işlemi başlatıldı.")
    trendyol_products = db.get_products_for_supplier("Trendyol")

    if not trendyol_products:
        print("Veritabanında Trendyol için ürün bulunamadı.")
        return

    for product in trendyol_products:
        print(f"İşleniyor: Ürün ID {product['product_id']} - URL: {product['supplier_product_url']}")
        price = await scrape_trendyol_price(product['supplier_product_url'])
        if price:
            db.update_price(product['product_supplier_id'], price)
    
    print("Trendyol işlemi tamamlandı.")

if __name__ == '__main__':
    asyncio.run(main())
